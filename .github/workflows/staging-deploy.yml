name: Deploy to Staging

on:
  push:
    branches: [ staging ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Notify Deployment Start
      run: |
        echo "🚀 Starting staging deployment..."
        # Send email notification
        curl -s -X POST \
          -H "Content-Type: application/json" \
          -d '{
            "to": "<EMAIL>",
            "subject": "🚀 Staging Deployment Started",
            "text": "Deployment to staging server has started.\nBranch: staging\nTriggered by: ${{ github.actor }}\nCommit: ${{ github.sha }}"
          }' \
          https://api.emailjs.com/api/v1.0/email/send || echo "Email notification failed (continuing deployment)"

    - name: Deploy to Staging Server
      id: deploy
      run: |
        echo "Starting Staging Deployment..."

        # Create SSH directory and set permissions
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh

        # Decode and save the EC2 private key
        echo "${{ secrets.STAGING_SSH_PRIVATE_KEY }}" | base64 -d > ~/.ssh/ec2-key
        chmod 600 ~/.ssh/ec2-key

        # Add staging EC2 to known hosts
        ssh-keyscan -H ec2-54-84-11-94.compute-1.amazonaws.com >> ~/.ssh/known_hosts

        # SSH into the server and deploy
        ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2-key <EMAIL> "bash -s" <<'EOF'
        set -e

        # Function for rollback
        rollback() {
          echo "❌ Deployment failed! Attempting rollback..."
          cd /var/www/successionplanai
          git reset --hard HEAD~1 2>/dev/null || echo "Rollback failed - manual intervention required"
          php artisan up 2>/dev/null || echo "Failed to bring app back up"
          exit 1
        }

        # Set trap for errors
        trap rollback ERR

        echo "📍 Changing to project directory"
        cd /var/www/successionplanai

        echo "🔧 Setting remote origin"
        git remote set-url origin https://${{ secrets.GH_PAT }}@github.com/SuccessionplanAI/successionplan-ai.git

        echo "🚫 PHASE 0: Putting application in maintenance mode"
        php artisan down --message="Deployment in progress. We'll be back shortly!" --retry=60

        echo "📁 Fixing permissions before git operations"
        sudo chown -R ubuntu:www-data /var/www/successionplanai/storage
        sudo chmod -R 775 /var/www/successionplanai/storage
        sudo chmod -R 775 /var/www/successionplanai/bootstrap/cache

        echo "📥 PHASE 1: Pulling code changes"
        git fetch origin
        git reset --hard origin/staging
        # Clean but exclude the virtual environment directory
        git clean -fd -e venv/

        echo "📁 Fixing permissions after git operations"
        sudo chown -R ubuntu:www-data /var/www/successionplanai/storage
        sudo chmod -R 775 /var/www/successionplanai/storage

        echo "📁 Creating and setting permissions for shared directory"
        sudo mkdir -p /var/www/successionplanai/shared
        sudo chown -R ubuntu:www-data /var/www/successionplanai/shared
        sudo chmod -R 775 /var/www/successionplanai/shared

        echo "📦 PHASE 2: Installing dependencies"
        composer install --no-interaction --prefer-dist --no-dev --optimize-autoloader

        echo "📦 Installing NPM packages"
        npm install --legacy-peer-deps

        echo "🏗️ Building assets"
        npm run build

        echo "🗄️ PHASE 3: Running database migrations"
        php artisan migrate --force

        echo "🔄 PHASE 4: Environment cleanup"
        # Clear all caches using optimize:clear (clears cache, route, view, config)
        php artisan optimize:clear

        # Restart Horizon queues (since you have Horizon configured)
        echo "🔄 Restarting Horizon queues"
        php artisan horizon:terminate || echo "Horizon not running, will start fresh"

        # Restart PHP-FPM
        echo "🔄 Restarting PHP-FPM"
        echo "" | sudo -S service php8.1-fpm reload

        echo "📁 Final permission check for shared directories"
        sudo chown -R www-data:www-data /var/www/successionplanai/shared
        sudo chmod -R 777 /var/www/successionplanai/shared
        echo "✅ Shared directory permissions updated"

        echo "✅ PHASE FINAL: Bringing application back online"
        php artisan up

        echo "🎉 Deployment completed successfully!"
        EOF

    - name: Notify Deployment Success
      if: success()
      run: |
        echo "✅ Deployment successful!"
        # Send success email notification
        curl -s -X POST \
          -H "Content-Type: application/json" \
          -d '{
            "to": "<EMAIL>",
            "subject": "✅ Staging Deployment Successful",
            "text": "Deployment to staging server completed successfully!\nBranch: staging\nCommit: ${{ github.sha }}\nDeployed by: ${{ github.actor }}\nTime: $(date)"
          }' \
          https://api.emailjs.com/api/v1.0/email/send || echo "Success notification failed"

    - name: Notify Deployment Failure
      if: failure()
      run: |
        echo "❌ Deployment failed!"
        # Send failure email notification
        curl -s -X POST \
          -H "Content-Type: application/json" \
          -d '{
            "to": "<EMAIL>",
            "subject": "❌ Staging Deployment Failed",
            "text": "Deployment to staging server failed!\nBranch: staging\nCommit: ${{ github.sha }}\nTriggered by: ${{ github.actor }}\nPlease check the GitHub Actions logs for details."
          }' \
          https://api.emailjs.com/api/v1.0/email/send || echo "Failure notification failed"
