#!/bin/bash

# Local Package Installation Script
# Run this script locally to install packages that were being installed during deployment
# This ensures proper dependency management and faster deployments

echo "🚀 Installing missing packages locally..."
echo "This script installs packages that should be committed to your lock files"
echo ""

# Check if we're in the right directory
if [ ! -f "composer.json" ] || [ ! -f "package.json" ]; then
    echo "❌ Error: composer.json or package.json not found!"
    echo "Please run this script from your Laravel project root directory."
    exit 1
fi

echo "📦 Installing Composer packages..."

# Install PHP packages that were being installed during deployment
echo "Installing rap2hpoutre/fast-excel..."
composer require rap2hpoutre/fast-excel

echo "Installing phpoffice/phpword..."
composer require phpoffice/phpword

echo ""
echo "📦 Installing NPM packages..."

# Install NPM packages that were being installed during deployment
echo "Installing @inertiajs/inertia..."
npm install @inertiajs/inertia

echo "Installing sweetalert2..."
npm install sweetalert2

echo "Installing react-window..."
npm install react-window

echo ""
echo "✅ Package installation completed!"
echo ""
echo "📋 Next steps:"
echo "1. Test your application locally to ensure everything works"
echo "2. Commit the updated composer.lock and package-lock.json files:"
echo "   git add composer.lock package-lock.json"
echo "   git commit -m 'Add missing packages to lock files'"
echo "3. Push to your repository:"
echo "   git push origin staging"
echo ""
echo "🎯 Benefits:"
echo "- Faster deployments (no package downloads during deployment)"
echo "- More reliable deployments (no network dependency issues)"
echo "- Consistent package versions across environments"
echo "- Proper dependency resolution"
